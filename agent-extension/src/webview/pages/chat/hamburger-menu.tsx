import React, { useCallback, useContext, useEffect, useState } from 'react';
import { Menu } from 'lucide-react';
import { DropdownMenu } from '@radix-ui/themes';
import { RpcContext } from '../../core/rpc/rpc-context';

interface HamburgerMenuProps {
  currentConversationId: string;
  onBusinessChange?: (business: string) => void;
}

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ currentConversationId, onBusinessChange }) => {
  const [businessList, setBusinessList] = useState<string[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<string>('');
  const [isLoadingBusinessList, setIsLoadingBusinessList] = useState(false);
  const [isChangingBusiness, setIsChangingBusiness] = useState(false);

  const rpcClient = useContext(RpcContext);

  const setConversationBusiness = useCallback(
    async (business: string) => {
      try {
        setIsChangingBusiness(true);
        setSelectedBusiness(business);
        if (!rpcClient || !currentConversationId) return;
        await rpcClient.call('setConversationBusiness', { cid: currentConversationId, business });
        // 通知父组件业务选择已更改
        onBusinessChange?.(business);
      } finally {
        setIsChangingBusiness(false);
      }
    },
    [rpcClient, currentConversationId, onBusinessChange],
  );

  // 获取 business 列表
  useEffect(() => {
    console.info('getConversationBusinessList', currentConversationId);
    if (!rpcClient) return;

    const fetchBusinessList = async () => {
      try {
        setIsLoadingBusinessList(true);
        const list: string[] = await rpcClient.call('getConversationBusinessList', {});
        setBusinessList(list);

        if (!currentConversationId) {
          await setConversationBusiness(list[0]);
          return;
        }

        const business: string = await rpcClient.call('getConversationBusiness', { cid: currentConversationId });
        await setConversationBusiness(business || list[0]);
      } finally {
        setIsLoadingBusinessList(false);
      }
    };

    fetchBusinessList();
  }, [rpcClient, currentConversationId, setConversationBusiness]);

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger>
        <div className="hamburger-menu-button" title="菜单">
          <Menu size={14} />
        </div>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content className="hamburger-menu-content">
        <DropdownMenu.Sub>
          <DropdownMenu.SubTrigger className="hamburger-menu-text">业务</DropdownMenu.SubTrigger>
          <DropdownMenu.SubContent className="hamburger-menu-sub-content">
            {isLoadingBusinessList ? (
              <DropdownMenu.Item className="hamburger-menu-text" disabled>
                加载中...
              </DropdownMenu.Item>
            ) : (
              businessList.map((business) => (
                <DropdownMenu.Item
                  className="hamburger-menu-text"
                  key={business}
                  onSelect={() => setConversationBusiness(business)}
                  disabled={isChangingBusiness}
                >
                  {selectedBusiness === business ? '✓ ' : ''}
                  {isChangingBusiness && business === selectedBusiness ? '切换中...' : business}
                </DropdownMenu.Item>
              ))
            )}
          </DropdownMenu.SubContent>
        </DropdownMenu.Sub>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
